# ========================================
# Sumo 插件配置文件
# ========================================

# 位置配置 - 用于设置大厅和竞技场的位置
location:
  lobby: world,-86.52884568687061,66.0,-5.010606043739387,-33.450012,3.1499996

# 黑名单世界 - 在这些世界中禁用插件功能
blacklist-worlds: []

# 基本选项配置
option:
  # 是否在大厅使用记分板显示
  use-lobby-scoreboard: true
  
  # 是否在大厅给玩家物品
  use-lobby-items: true
  
  # 是否在大厅使用自定义标签页名称
  use-lobby-tablist-name: true
  
  # 是否启用奖励系统
  rewards: true
  
  # 数据保存类型 (FILE: 文件保存, MYSQL: 数据库保存)
  save-type: FILE
  
  # 玩家加入时是否自动传送到大厅
  join-teleport: true
  
  # 是否禁用玩家加入消息
  disable-join-message: true
  
  # 是否禁用玩家退出消息
  disable-quit-message: true

# 竞技场配置
arena:
  # 游戏时间 (分钟)
  time: 2
  
  # 在竞技场中允许使用的命令列表
  allowed-commands:
  - /mute
  - /list
  
  # 是否显示Boss血条
  bossbar: true
  
  # 是否允许在竞技场中聊天
  chat: true

# BungeeCord 跨服配置
bungeecord:
  # 是否启用 BungeeCord 支持
  enabled: false
  
  # 是否自动让玩家加入游戏
  auto-join-players: false
  
  # 是否自动让玩家以观察者身份加入
  auto-join-spectate: false
  
  # 服务器名称 (用于 BungeeCord 环境)
  server: sumo

# MySQL 数据库配置 (当 save-type 设置为 MYSQL 时使用)
mysql:
  # 数据库主机地址
  host: localhost
  
  # 数据库端口
  port: 3306
  
  # 数据库用户名
  user: sumo
  
  # 数据库密码
  password: sumo
  
  # 数据库名称
  database: sumo
