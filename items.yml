lobby:
  play:
    enable: true
    slot: 0
    type: PLAY
    item:
      material: BOOK
      data: 0
      amount: 1
      displayName: '&a&lPlay &7(Right Click)'
      lores:
      - '&7Right-click to play game arena!'
  stats:
    enable: true
    slot: 1
    type: STATS
    item:
      material: PAPER
      data: 0
      amount: 1
      displayName: '&b&lStats &7(Right Click)'
      lores:
      - '&7Right-click to view your stats!'
ingame:
  gameleave:
    enable: true
    slot: 8
    type: LEAVE
    item:
      material: RED_BED
      data: 0
      amount: 1
      displayName: '&c&lReturn to Lobby &7(Right Click)'
      lores:
      - '&7Right-click to leave to leave lobby!'
spectator:
  teleporter:
    enable: true
    slot: 0
    type: TELEPORTER
    item:
      material: COMPASS
      data: 0
      amount: 1
      displayName: '&a&lTeleporter &7(Right Click)'
      lores:
      - '&7Right-click to spectate players!'
  spectatorsettings:
    enable: true
    slot: 4
    type: SPECTATOR_SETTINGS
    item:
      material: COMPARATOR
      data: 0
      amount: 1
      displayName: '&b&lSpectator Settings &7(Right Click)'
      lores:
      - '&7Right-click to change your spectator settings!'
  playagain:
    enable: true
    slot: 7
    type: PLAY_AGAIN
    item:
      material: PAPER
      data: 0
      amount: 1
      displayName: '&b&lPlay Again &7(Right Click)'
      lores:
      - '&7Right-click to play another game!'
  gameleave:
    enable: true
    slot: 8
    type: LEAVE
    item:
      material: RED_BED
      data: 0
      amount: 1
      displayName: '&c&lReturn to Lobby &7(Right Click)'
      lores:
      - '&7Right-click to leave to leave lobby!'
setup:
  set_waiting:
    enable: true
    slot: 0
    type: SET_WAITING_LOCATION
    item:
      material: PAPER
      data: 0
      amount: 1
      displayName: '&b&lSet Waiting Location &7(Right Click)'
      lores:
      - '&7Right-click to Set to waiting location!'
  set_spec:
    enable: true
    slot: 1
    type: SET_SPEC_LOCATION
    item:
      material: PAPER
      data: 0
      amount: 1
      displayName: '&b&lSet Spectator Location &7(Right Click)'
      lores:
      - '&7Right-click to Set to spectator location!'
  set_a_team:
    enable: true
    slot: 3
    type: SET_A_TEAM_LOCATION
    item:
      material: BLUE_WOOL
      data: 11
      amount: 1
      displayName: '&b&lSet A Team Location &7(Right Click)'
      lores:
      - '&7Right-click to Set to A team location!'
  set_b_team:
    enable: true
    slot: 4
    type: SET_B_TEAM_LOCATION
    item:
      material: RED_WOOL
      data: 14
      amount: 1
      displayName: '&b&lSet B Team Location &7(Right Click)'
      lores:
      - '&7Right-click to Set to B team location!'
  set_min_player_y:
    enable: true
    slot: 5
    type: SET_MIN_PLAYER_Y
    item:
      material: IRON_INGOT
      data: 0
      amount: 1
      displayName: '&c&lSet Min Player Y Location &7(Right Click)'
      lores:
      - '&7Right-click to Set to Min Player Y From Your Location!'
  save:
    enable: true
    slot: 8
    type: ARENA_SAVE
    item:
      material: RED_BED
      data: 0
      amount: 1
      displayName: '&c&lSave and Quit &7(Right Click)'
      lores:
      - '&7Right-click to Save to arena!'
gui-stats:
  my-stats:
    enable: true
    slot: 13
    type: MY_STATS
    item:
      material: PLAYER_HEAD
      data: '%%player%%'
      amount: 1
      displayName: '&cMy Stats'
      lores:
      - ''
      - '&fName: &a%%player%%'
      - '&fKills: &a%%kills%%'
      - '&fWins: &a%%wins%%'
      - '&fDeaths: &a%%deaths%%'
      - '&fBest Streaks: &a%%beststreaks%%'
      - ''
  close:
    enable: true
    slot: 22
    type: close
    item:
      material: BARRIER
      data: 0
      amount: 1
      displayName: '&cClose'
      lores: []
gui-spectatorsettings:
  no-speed-item:
    enable: true
    slot: 11
    type: NO_SPEED
    item:
      material: LEATHER_BOOTS
      data: 0
      amount: 1
      displayName: '&aNo Speed'
      lores: []
  speed-one-item:
    enable: true
    slot: 12
    type: SPEED_I
    item:
      material: CHAINMAIL_BOOTS
      data: 0
      amount: 1
      displayName: '&aSpeed I'
      lores: []
  speed-two-item:
    enable: true
    slot: 13
    type: SPEED_II
    item:
      material: GOLDEN_BOOTS
      data: 0
      amount: 1
      displayName: '&aSpeed II'
      lores: []
  speed-three-item:
    enable: true
    slot: 14
    type: SPEED_III
    item:
      material: IRON_BOOTS
      data: 0
      amount: 1
      displayName: '&aSpeed III'
      lores: []
  speed-four-item:
    enable: true
    slot: 15
    type: SPEED_IV
    item:
      material: DIAMOND_BOOTS
      data: 0
      amount: 1
      displayName: '&aSpeed IV'
      lores: []
  auto-teleport-enable-item:
    enable: true
    slot: 20
    type: AUTO_TELEPORT_ENABLED
    item:
      material: COMPASS
      data: 0
      amount: 1
      displayName: '&aEnable Auto Teleport'
      lores:
      - '&7Click to enable auto teleport!'
  auto-teleport-disable-item:
    enable: true
    slot: 20
    type: AUTO_TELEPORT_DISABLED
    item:
      material: COMPASS
      data: 0
      amount: 1
      displayName: '&cDisable Auto Teleport'
      lores:
      - '&7Click to disable auto teleport!'
  night-vision-enable-item:
    enable: true
    slot: 21
    type: NIGHT_VISION_ENABLED
    item:
      material: ENDER_PEARL
      data: 0
      amount: 1
      displayName: '&aEnable Night Vision'
      lores:
      - '&7Click to enable night vision!'
  night-vision-disable-item:
    enable: true
    slot: 21
    type: NIGHT_VISION_DISABLED
    item:
      material: ENDER_PEARL
      data: 0
      amount: 1
      displayName: '&cDisable Night Vision'
      lores:
      - '&7Click to disable night vision!'
  first-person-enable-item:
    enable: true
    slot: 23
    type: FIRST_PERSON_ENABLED
    item:
      material: CLOCK
      data: 0
      amount: 1
      displayName: '&aEnable First Person'
      lores:
      - '&7Click to enable first person'
      - '&7spectating when using the'
      - '&7compass!'
      - '&7You can also right-click a'
      - '&7player to spectate them in'
      - '&7first person.'
  first-person-disable-item:
    enable: true
    slot: 23
    type: FIRST_PERSON_DISABLED
    item:
      material: CLOCK
      data: 0
      amount: 1
      displayName: '&cDisable First Person'
      lores:
      - '&7Click to disable first person'
      - '&7spectating when using the'
      - '&7compass!'
      - '&7You can also right-click a'
      - '&7player to spectate them in'
      - '&7first person.'
  toggle-spectators-enable-item:
    enable: true
    slot: 24
    type: TOGGLE_SPECTATORS_ENABLED
    item:
      material: GLOWSTONE_DUST
      data: 0
      amount: 1
      displayName: '&aShow Spectators'
      lores:
      - '&7Click to show other spectators!'
  toggle-spectators-disable-item:
    enable: true
    slot: 24
    type: TOGGLE_SPECTATORS_DISABLED
    item:
      material: REDSTONE
      data: 0
      amount: 1
      displayName: '&cHide Spectators'
      lores:
      - '&7Click to hide other spectators!'
player-visible-items:
  player-visible-enable-item:
    enable: true
    slot: 7
    type: PLAYER_VISIBLE_ENABLE
    item:
      material: LIME_DYE
      data: 10
      amount: 1
      displayName: '&fPlayers: &aVisible &7(Right Click)'
      lores:
      - '&7Right-click to toggle player visibility!'
  player-visible-disable-item:
    enable: true
    slot: 7
    type: PLAYER_VISIBLE_DISABLE
    item:
      material: GRAY_DYE
      data: 8
      amount: 1
      displayName: '&fPlayers: &cHidden &7(Right Click)'
      lores:
      - '&7Right-click to toggle player visibility!'
